import CommonHeader from "../../components/layout/common_header";
import CommonFooter from "../../components/layout/common_footer";
import TopBar from "../../components/layout/topBar";

export default function Dashboard() {
  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid">
            <div className="row">
              <div className="col-12">
                <div className="card">
                  <div className="card-body p-4 pb-0" data-simplebar="">
                    <div className="row flex-nowrap">
                      <div className="col">
                        <div className="card primary-gradient">
                          <div className="card-body text-center px-9 pb-4">
                            <div className="d-flex align-items-center justify-content-center round-48 rounded text-bg-primary flex-shrink-0 mb-3 mx-auto">
                              <iconify-icon
                                icon="solar:dollar-minimalistic-linear"
                                className="fs-7 text-white"
                              ></iconify-icon>
                            </div>
                            <h6 className="fw-normal fs-3 mb-1">
                              Total Orders
                            </h6>
                            <h4 className="mb-3 d-flex align-items-center justify-content-center gap-1">
                              16,689
                            </h4>
                            <a
                              href="javascript:void(0)"
                              className="btn btn-white fs-2 fw-semibold text-nowrap"
                            >
                              View Details
                            </a>
                          </div>
                        </div>
                      </div>
                      <div className="col">
                        <div className="card warning-gradient">
                          <div className="card-body text-center px-9 pb-4">
                            <div className="d-flex align-items-center justify-content-center round-48 rounded text-bg-warning flex-shrink-0 mb-3 mx-auto">
                              <iconify-icon
                                icon="solar:recive-twice-square-linear"
                                className="fs-7 text-white"
                              ></iconify-icon>
                            </div>
                            <h6 className="fw-normal fs-3 mb-1">Return Item</h6>
                            <h4 className="mb-3 d-flex align-items-center justify-content-center gap-1">
                              148
                            </h4>
                            <a
                              href="javascript:void(0)"
                              className="btn btn-white fs-2 fw-semibold text-nowrap"
                            >
                              View Details
                            </a>
                          </div>
                        </div>
                      </div>
                      <div className="col">
                        <div className="card secondary-gradient">
                          <div className="card-body text-center px-9 pb-4">
                            <div className="d-flex align-items-center justify-content-center round-48 rounded text-bg-secondary flex-shrink-0 mb-3 mx-auto">
                              <iconify-icon
                                icon="ic:outline-backpack"
                                className="fs-7 text-white"
                              ></iconify-icon>
                            </div>
                            <h6 className="fw-normal fs-3 mb-1">
                              Annual Budget
                            </h6>
                            <h4 className="mb-3 d-flex align-items-center justify-content-center gap-1">
                              $156K
                            </h4>
                            <a
                              href="javascript:void(0)"
                              className="btn btn-white fs-2 fw-semibold text-nowrap"
                            >
                              View Details
                            </a>
                          </div>
                        </div>
                      </div>
                      <div className="col">
                        <div className="card danger-gradient">
                          <div className="card-body text-center px-9 pb-4">
                            <div className="d-flex align-items-center justify-content-center round-48 rounded text-bg-danger flex-shrink-0 mb-3 mx-auto">
                              <iconify-icon
                                icon="ic:baseline-sync-problem"
                                className="fs-7 text-white"
                              ></iconify-icon>
                            </div>
                            <h6 className="fw-normal fs-3 mb-1">
                              Cancel Orders
                            </h6>
                            <h4 className="mb-3 d-flex align-items-center justify-content-center gap-1">
                              64
                            </h4>
                            <a
                              href="javascript:void(0)"
                              className="btn btn-white fs-2 fw-semibold text-nowrap"
                            >
                              View Details
                            </a>
                          </div>
                        </div>
                      </div>
                      <div className="col">
                        <div className="card success-gradient">
                          <div className="card-body text-center px-9 pb-4">
                            <div className="d-flex align-items-center justify-content-center round-48 rounded text-bg-success flex-shrink-0 mb-3 mx-auto">
                              <iconify-icon
                                icon="ic:outline-forest"
                                className="fs-7 text-white"
                              ></iconify-icon>
                            </div>
                            <h6 className="fw-normal fs-3 mb-1">
                              Total Income
                            </h6>
                            <h4 className="mb-3 d-flex align-items-center justify-content-center gap-1">
                              $36,715
                            </h4>
                            <a
                              href="javascript:void(0)"
                              className="btn btn-white fs-2 fw-semibold text-nowrap"
                            >
                              View Details
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CommonFooter />
    </>
  );
}
