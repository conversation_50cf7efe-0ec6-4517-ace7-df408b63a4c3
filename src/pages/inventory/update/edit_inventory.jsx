import CommonHeader from "../../../components/layout/common_header";
import CommonFooter from "../../../components/layout/common_footer";
import TopBar from "../../../components/layout/topBar";
import Breadcrumb from "../../../components/breadcrumb";
import { handleApiErrors } from "../../../hooks/handleApiErrors";
import { handleApiSuccess } from "../../../hooks/handleApiSucess";
import { Formik, Form } from "formik";
import * as yup from "yup";
import FormikField from "../../../components/formikField";
import {
  useGetVariationsProductsMutation
} from "../../../feature/api/productDataApiSlice";
import { useLocation, useNavigate } from "react-router-dom";
import { useGetAllAttributesValuesQuery } from "../../../feature/api/attributesValuesDataApiSlice";
import { useListAllAttributesQuery } from "../../../feature/api/attributesDataApiSlice";
import { useMemo, useState, useEffect } from "react";
import { useGetShopBranchsQuery } from "../../../feature/api/branchDataApiSlice";
import { useEditInventoryMutation, useGetInventoryHistoryListQuery } from "../../../feature/api/inventoryDataApiSlice";
import { useGetGeneralStatusQuery } from "../../../feature/api/statusApiSlice";
import { PaginationComponent } from "../../../components/pagination";
import { Table } from "../../../components/datatable";
import WebLoader from "../../../components/webLoader";

const AttributeValueSelect = ({ attributeId, attributeTitle, attributesArray = [], formikProps }) => {
  const { data: attributeValuesData, isLoading, error } = useGetAllAttributesValuesQuery(
    { attribute_id: attributeId },
    { skip: !attributeId }
  );

  const attributeValueOptions = useMemo(() => {
    if (!attributeValuesData?.data?.length) {
      return [];
    }
    return attributeValuesData.data.map((value) => ({
      value: value.id,
      label: value.title,
    }));
  }, [attributeValuesData?.data]);

  // Set the pre-selected value when attribute values are loaded
  useEffect(() => {
    if (attributeValuesData?.data?.length && attributesArray.length && formikProps) {
      const fieldName = `attribute_${attributeId}`;
      const currentValue = formikProps.values[fieldName];

      // Only set if field is currently empty
      if (!currentValue || currentValue === "") {
        const preSelectedValue = attributeValuesData.data.find(value =>
          attributesArray.includes(value.id)
        );

        if (preSelectedValue) {
          formikProps.setFieldValue(fieldName, preSelectedValue.id.toString());
        }
      }
    }
  }, [attributeValuesData?.data, attributesArray, attributeId, formikProps]);

  return (
    attributeValueOptions.length > 0 && !isLoading && !error && (
      <div className="col-lg-6">
        <div className="mb-3">
          <label htmlFor={`attribute_${attributeId}`} className="form-label">
            {attributeTitle}
            <span className="un-validation">(*)</span>
            {isLoading && <small className="text-muted"> (Loading...)</small>}
          </label>
          <FormikField
            name={`attribute_${attributeId}`}
            id={`attribute_${attributeId}`}
            className="form-select"
            type="select"
            options={attributeValueOptions}
          />
          {error && <small className="text-danger">Error loading attribute values</small>}
        </div>
      </div>
    )
  );
};

const getInitialValues = (productAttributes, inventoryData = {}) => {
  const baseValues = {
    category_id: inventoryData.category_id || "",
    sub_category_id: inventoryData.sub_category_id || "",
    child_category_id: inventoryData.child_category_id || "",
    branch_id: inventoryData.branch_id || "",
    product_id: inventoryData.product_id || "",
    bar_code: inventoryData.bar_code || "",
    original_price: inventoryData.original_price || "",
    selling_price: inventoryData.selling_price || "",
    old_price: inventoryData.old_price || "",
    description: inventoryData.description || "",
    description_ar: inventoryData.description_ar || "",
    stock_quantity: inventoryData.stock_quantity || "",
    stock_status: inventoryData.stock_status || "",
    sku: inventoryData.sku || "",
    is_publish: inventoryData.is_publish || "",
  };

  // Add attribute fields - we'll handle pre-selection through a different mechanism
  productAttributes.forEach(attr => {
    const fieldName = `attribute_${attr.attribute_id}`;
    baseValues[fieldName] = "";
  });

  return baseValues;
};
const getValidationSchema = () => {
  const baseValidation = {
    branch_id: yup.string().required().label("Branch"),
    product_id: yup.string().required().label("Product"),
    bar_code: yup.string().required().label("Barcode"),
    sku: yup.string().required().label("Sku"),
    original_price: yup.number().required().label("Original Price"),
    selling_price: yup.number().required().label("Selling Price"),
    old_price: yup.number().label("Old Price"),
    stock_quantity: yup.number().required().label("Stock Quantity"),
    stock_status: yup.string().required().label("Stock Status"),
    description: yup.string().label("Description"),
    description_ar: yup.string().label("Description Arabic"),
    is_publish: yup.string().required().label("Status"),
  };

  return yup.object().shape(baseValidation);
};

export default function EditInventory() {
  const { state } = useLocation();
  const inventoryData = state;
  
  const navigate = useNavigate(); // Initialize the navigation hook
  const activePage = "Inventory";
  const linkHref = "/dashboard";

  /* **************** Start list all Inventory ******************* */
const [currentPage, setCurrentPage] = useState(1);
const { data: inventoryListResp, isLoading } = useGetInventoryHistoryListQuery({
    page: currentPage,
  });
  const inventoryList = useMemo(() => {
    if (!inventoryListResp?.data.list?.length) {
      if (currentPage > 1) setCurrentPage((current) => current - 1);
      return [];
    }
    return inventoryListResp?.data?.list;
  }, [currentPage, inventoryListResp?.data.list]);
  const pageData = useMemo(
    () => inventoryListResp?.data?.page ?? null,
    [inventoryListResp]
  );

  /* **************** End list all Inventory ******************* */

  /* **************** Start Paginatation ***************** */
    const fetchData = async (page) => {
      try {
        setCurrentPage(page); // Update the page state
      } catch (error) {
        handleApiErrors(error);
      }
    };
    /* **************** End Paginatation ***************** */


  /* **************** Start stock status List ******************* */
  const stockStatusList = [
    { value: 1,
      label: "In Stock",
    },
    {
      value: 2,
      label: "Out of Stock"
    }
  ];
  /* **************** End stock status List ******************* */

    /* **************** Start list General Status ******************* */
    const generalStatuData = useGetGeneralStatusQuery();
    const generalStatuDataList = generalStatuData?.data?.data || [];
    const generalStatusList = generalStatuDataList.map((values) => ({
      value: values.id,
      label: values.status,
    }));
    /* **************** Start list General Status ******************* */

  /* **************** Start Branch List ******************* */
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchesList = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name + ' (' + values.branch_type_name + ')',
  }));
  /* **************** End Branch List ******************* */

  /* **************** Start Fetch Product Variations and Attributes ******************* */
  const [getVariationsProducts] = useGetVariationsProductsMutation();
  const [productAttributes, setProductAttributes] = useState([]);

  // Fetch product variations when product is selected
  useEffect(() => {
    if (inventoryData.product_id && inventoryData.product_id !== "") {
      const fetchProductVariations = async () => {
        try {
          const response = await getVariationsProducts({ product_id: parseInt(inventoryData.product_id) }).unwrap();
          if (response?.data && Array.isArray(response.data) && response.data.length > 0) {
            setProductAttributes(response.data);
          } else {
            setProductAttributes([]);
          }
        } catch (error) {
          console.error("Error fetching product variations:", error);
          setProductAttributes([]);
        }
      };
      fetchProductVariations();
    } else {
      setProductAttributes([]);
    }
  }, [inventoryData.product_id, getVariationsProducts]);

  const { data: allAttributesData } = useListAllAttributesQuery();
  const attributesMap = useMemo(() => {
    if (!allAttributesData?.data) return {};
    return allAttributesData.data.reduce((acc, attr) => {
      acc[attr.id] = attr.title;
      return acc;
    }, {});
  }, [allAttributesData?.data]);
  /* **************** End Fetch Product Variations and Attributes ******************* */

  /* **************** Start Edit Inventory ******************* */
  const [handleEditInventoryApi] = useEditInventoryMutation();
  const handleSubmit = async (body) => {
    try {
      const attributes = [];
      Object.keys(body).forEach(key => {
        if (key.startsWith('attribute_') && body[key] && body[key] !== "") {
          attributes.push(parseInt(body[key]));
        }
      });

      const updateData = {
        id: inventoryData.id,
        branch_id: parseInt(body.branch_id),
        product_id: parseInt(body.product_id),
        attributes: attributes.length > 0 ? JSON.stringify(attributes) : null,
        stock_quantity: parseInt(body.stock_quantity),
        stock_status: parseInt(body.stock_status),
        sku: body.sku,
        bar_code: body.bar_code,
        original_price: body.original_price,
        selling_price: body.selling_price,
        old_price: body.old_price || "",
        description: body.description || "",
        description_ar: body.description_ar || "",
        is_publish: parseInt(body.is_publish)
      };

      const resp = await handleEditInventoryApi(updateData).unwrap();
      handleApiSuccess(resp);
      navigate("/inventory");
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Esit Inventory ******************* */


/* **************** Web Loader  ******************* */
  if (isLoading)
    return <WebLoader />;
  /* **************** End Web Loader  ******************* */
  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid ">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card">
              <div className="card-body">
                <div className="tab-content" id="pills-tabContent">
                  <div
                    className="tab-pane fade show active"
                    id="pills-account"
                    role="tabpanel"
                    aria-labelledby="pills-account-tab"
                    tabIndex="0"
                    >
                    <div className="row">
                      <div className="col-lg-12 d-flex align-items-stretch">
                        <div className="card w-100 border position-relative overflow-hidden mb-0">
                          <div className="card-body p-4">
                            <h4 className="card-title">Edit Inventory</h4>
                            <p className="card-subtitle mb-4">
                              To update Inventory, fill details and save from here
                            </p>
                            <Formik
                              initialValues={getInitialValues(productAttributes, inventoryData)}
                              enableReinitialize={true}
                              validationSchema={getValidationSchema(attributesMap)}
                              onSubmit={handleSubmit}
                            >
                              {(formikProps) => (
                                <Form
                                  name="product-create"
                                  className="needs-validation"
                                  autoComplete="off"
                                >
                                  <div className="row">
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label htmlFor="branch_id" className="form-label">
                                        Branch
                                        <span className="un-validation">(*)</span>
                                      </label>
                                      <FormikField
                                        name="branch_id"
                                        id="branch_id"
                                        className="form-select"
                                        type="select"
                                        options={branchesList}
                                      />
                                    </div>
                                    </div> 
                                    {/* Start Dynamic Attribute Value Select Fields */}
                                    {productAttributes.length > 0 && (
                                      <>
                                        {productAttributes.map((attr) => (
                                          <AttributeValueSelect
                                            key={attr.attribute_id}
                                            attributeId={attr.attribute_id}
                                            attributeTitle={attributesMap[attr.attribute_id] || `Attribute ${attr.attribute_id}`}
                                            attributesArray={inventoryData.attributes_array || []}
                                            formikProps={formikProps}
                                          />
                                        ))}
                                      </>
                                    )}
                                    {/* End Dynamic Attribute Value Select Fields */}
                                    <>                                    
                                      
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="bar_code" className="form-label">
                                            Barcode
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            type="text"
                                            name="bar_code"
                                            id="bar_code"
                                            placeholder="Enter barcode"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="sku" className="form-label">
                                            Sku
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            type="text"
                                            name="sku"
                                            id="sku"
                                            placeholder="Enter sku"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>

                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="original_price" className="form-label">
                                            Original Price
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            type="number"
                                            name="original_price"
                                            id="original_price"
                                            placeholder="Enter orignal price"
                                            className="form-control"
                                            step="0.01"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="old_price" className="form-label">
                                            Old Price
                                          </label>
                                          <FormikField
                                            type="number"
                                            name="old_price"
                                            id="old_price"
                                            placeholder="Enter old price"
                                            className="form-control"
                                            step="0.01"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="selling_price" className="form-label">
                                            Selling Price
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            type="number"
                                            name="selling_price"
                                            id="selling_price"
                                            placeholder="Enter selling price"
                                            className="form-control"
                                            step="0.01"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="stock_quantity" className="form-label">
                                            Stock Quantity
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            type="number"
                                            name="stock_quantity"
                                            id="stock_quantity"
                                            placeholder="Enter stock quantity"
                                            className="form-control"
                                            min="0"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="stock_status" className="form-label">
                                            Stock Status
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            type="select"
                                            name="stock_status"
                                            id="stock_status"
                                            className="form-select"
                                            options={stockStatusList}
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="description" className="form-label">
                                            Description
                                          </label>
                                          <FormikField
                                            type="textarea"
                                            name="description"
                                            id="description"
                                            placeholder="Enter description"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="description_ar" className="form-label">
                                            Description Arabic
                                          </label>
                                          <FormikField
                                            type="textarea"
                                            name="description_ar"
                                            id="description_ar"
                                            placeholder="Enter description arabic"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="branch_name" className="form-label">
                                            Status
                                            <span className="un-validation">(*)</span> :
                                          </label>
                                          <FormikField
                                            name="is_publish"
                                            id="is_publish"
                                            className="form-select"
                                            type="select"
                                            options={generalStatusList}
                                          />
                                        </div>
                                      </div>
                                    </>
                                  <div className="col-12">
                                    <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                      <button
                                        className="btn btn-primary"
                                        type="submit"
                                      >
                                        Update Inventory
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </Form>
                              )}
                            </Formik>
                            <Table
                                                headCells={[
                                                  { key: "sel_id", label: "#", align: "left" },
                                                  {
                                                    key: "product_name",
                                                    label: "Product Name",
                                                    align: "left",
                                                  },
                                                  {
                                                    key: "branch_name",
                                                    label: "Branch Name",
                                                    align: "left",
                                                  },
                                                  {
                                                    key: "sku",
                                                    label: "SKU",
                                                    align: "left",
                                                  },
                                                  {
                                                    key: "bar_code",
                                                    label: "Barcode",
                                                    align: "left",
                                                  },
                                                  {
                                                    key: "original_price",
                                                    label: "Original Price",
                                                    align: "left",
                                                  },
                                                  {
                                                    key: "selling_price",
                                                    label: "Selling Price",
                                                    align: "left",
                                                  },
                                                  {
                                                    key: "stock_quantity",
                                                    label: "Stock Quantity",
                                                    align: "left",
                                                  },
                                                  {
                                                    key: "stock_status_text",
                                                    key_id: "stock_status",
                                                    label: "Stock Status",
                                                    align: "left",
                                                  },
                                                  {
                                                    key: "action_name",
                                                    key_id: "action",
                                                    label: "Action",
                                                    align: "left",
                                                  },
                                                  {
                                                    key: "status_name",
                                                    key_id: "status",
                                                    label: "Status",
                                                    align: "left",
                                                  },                      
                                                  {
                                                    key: "created_at",
                                                    label: "Created At",
                                                    align: "left",
                                                  },
                                                ]}
                                                data={inventoryList}
                                              />
                                              <PaginationComponent
                                              totalCount={pageData?.total_count}
                                              pageSize={pageData?.page_size}
                                              currentPage={currentPage}
                                              setCurrentPage={setCurrentPage}
                                              onPageChange={fetchData}
                                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CommonFooter />
    </>
  );
}
