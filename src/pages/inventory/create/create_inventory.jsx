import CommonHeader from "../../../components/layout/common_header";
import CommonFooter from "../../../components/layout/common_footer";
import TopBar from "../../../components/layout/topBar";
import Breadcrumb from "../../../components/breadcrumb";
import { handleApiErrors } from "../../../hooks/handleApiErrors";
import { handleApiSuccess } from "../../../hooks/handleApiSucess";
import { Formik, Form } from "formik";
import * as yup from "yup";
import FormikField from "../../../components/formikField";
import {
  useGetVariationsProductsMutation
} from "../../../feature/api/productDataApiSlice";
import { useNavigate } from "react-router-dom";
import { useListAllCategoriesQuery } from "../../../feature/api/categoriesDataApiSlice";
import { useGetAllSubCategoriesQuery } from "../../../feature/api/subCategoriesDataApiSlice";
import { useGetAllChildCategoriesQuery } from "../../../feature/api/childCategoriesDataApiSlice";
import { useGetAllAttributesValuesQuery } from "../../../feature/api/attributesValuesDataApiSlice";
import { useListAllAttributesQuery } from "../../../feature/api/attributesDataApiSlice";
import { useMemo, useState, useEffect, useRef } from "react";
import { useListAllProductQuery } from "../../../feature/api/productDataApiSlice";
import { useGetShopBranchsQuery } from "../../../feature/api/branchDataApiSlice";
import { useCreateInventoryMutation } from "../../../feature/api/inventoryDataApiSlice";

const AttributeValueSelect = ({ attributeId, attributeTitle }) => {
  const { data: attributeValuesData, isLoading, error } = useGetAllAttributesValuesQuery(
    { attribute_id: attributeId },
    { skip: !attributeId }
  );

  const attributeValueOptions = useMemo(() => {
    if (!attributeValuesData?.data?.length) {
      return [];
    }
    return attributeValuesData.data.map((value) => ({
      value: value.id,
      label: value.title,
    }));
  }, [attributeValuesData?.data]);

  return (
    attributeValueOptions.length > 0 && !isLoading && !error && (
      <div className="col-lg-6">
        <div className="mb-3">
          <label htmlFor={`attribute_${attributeId}`} className="form-label">
            {attributeTitle}
            <span className="un-validation">(*)</span>
            {isLoading && <small className="text-muted"> (Loading...)</small>}
          </label>
          <FormikField
            name={`attribute_${attributeId}`}
            id={`attribute_${attributeId}`}
            className="form-select"
            type="select"
            options={attributeValueOptions}
          />
          {error && <small className="text-danger">Error loading attribute values</small>}
        </div>
      </div>
    )
  );
};

const getInitialValues = (productAttributes, currentValues = {}) => {
  const baseValues = {
    category_id: currentValues.category_id || "",
    sub_category_id: currentValues.sub_category_id || "",
    child_category_id: currentValues.child_category_id || "",
    branch_id: currentValues.branch_id || "",
    product_id: currentValues.product_id || "",
    bar_code: currentValues.bar_code || "",
    original_price: currentValues.original_price || "",
    selling_price: currentValues.selling_price || "",
    old_price: currentValues.old_price || "",
    description: currentValues.description || "",
    description_ar: currentValues.description_ar || "",
    stock_quantity: currentValues.stock_quantity || "",
    stock_status: currentValues.stock_status || "",
    sku: currentValues.sku || "",
  };

  // Add attribute fields, preserving existing values
  productAttributes.forEach(attr => {
    const fieldName = `attribute_${attr.attribute_id}`;
    baseValues[fieldName] = currentValues[fieldName] || "";
  });

  return baseValues;
};
const getValidationSchema = () => {
  const baseValidation = {
    branch_id: yup.string().required().label("Branch"),
    product_id: yup.string().required().label("Product"),
    bar_code: yup.string().required().label("Barcode"),
    sku: yup.string().required().label("Sku"),
    original_price: yup.number().required().label("Original Price"),
    selling_price: yup.number().required().label("Selling Price"),
    old_price: yup.number().label("Old Price"),
    stock_quantity: yup.number().required().label("Stock Quantity"),
    stock_status: yup.string().required().label("Stock Status"),
    description: yup.string().label("Description"),
    description_ar: yup.string().label("Description Arabic"),
  };

  return yup.object().shape(baseValidation);
};

const stockStatusList = [
  { value: 1,
    label: "In Stock",
  },
  {
    value: 2,
    label: "Out of Stock"
  }
];
export default function CreateInventory() {
  const navigate = useNavigate(); // Initialize the navigation hook
  const activePage = "Add Inventory";
  const linkHref = "/dashboard";

  /* **************** Start Branch List ******************* */
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchesList = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name + ' (' + values.branch_type_name + ')',
  }));
  /* **************** End Branch List ******************* */

  /* **************** Start Category List ******************* */
  const categoryListResp = useListAllCategoriesQuery();
  const categoryList = categoryListResp.data?.data || [];
  const categoriesList = categoryList.map((values) => ({
    value: values.id,
    label: values.title,
  }));
  /* **************** End Category List ******************* */

  /* **************** Start Category Selection State Management ******************* */
  const [selectedCategoryId, setSelectedCategoryId] = useState("");
  const [selectedSubCategoryId, setSelectedSubCategoryId] = useState("");
  const [selectedChildCategoryId, setSelectedChildCategoryId] = useState("");
  const [selectedProductId, setSelectedProductId] = useState("");

  // Track current form values to preserve them during reinitialization
  const currentFormValuesRef = useRef({});

  /* **************** Start Fetch Subcategories Based On Category (Load In Select Box) ******************* */
  const { data: subCategoriesData } = useGetAllSubCategoriesQuery(
    { category_id: parseInt(selectedCategoryId) },
    { skip: !selectedCategoryId || selectedCategoryId === "" }
  );
  const subCategoriesList = useMemo(() => {
    if (!subCategoriesData?.data?.length) {
      return [];
    }
    return subCategoriesData.data.map((values) => ({
      value: values.id,
      label: values.title,
    }));
  }, [subCategoriesData?.data]);
  /* **************** End Fetch Subcategories Based On Category (Load In Select Box) ******************* */

  /* **************** Start Fetch childCategories Based On SubCategory (Load In Select Box) ******************* */
  const { data: childCategoriesData } = useGetAllChildCategoriesQuery(
    { sub_category_id: parseInt(selectedSubCategoryId) },
    { skip: !selectedSubCategoryId || selectedSubCategoryId === "" }
  );
  const childCategoriesList = useMemo(() => {
    if (!childCategoriesData?.data?.length) {
      return [];
    }
    return childCategoriesData.data.map((values) => ({
      value: values.id,
      label: values.title,
    }));
  }, [childCategoriesData?.data]);
  /* **************** End Fetch childCategories Based On SubCategory (Load In Select Box) ******************* */

  /* **************** Start Handle category selection change ******************* */
  const handleCategoryChange = (e) => {
    const categoryId = e.target.value;
    setSelectedCategoryId(categoryId);
    setSelectedSubCategoryId("");
    setSelectedChildCategoryId("");
  };
  /* **************** End Handle category selection change ******************* */

  /* **************** Start Handle sub category selection change ******************* */
  const handleSubCategoryChange = (e) => {
    const subCategoryId = e.target.value;
    setSelectedSubCategoryId(subCategoryId);
    setSelectedChildCategoryId("");
  };
  /* **************** End Handle sub category selection change ******************* */

  /* **************** Start Handle child category selection change ******************* */
  const handleChildCategoryChange = (e) => {
    setSelectedChildCategoryId(e.target.value);
  };
  /* **************** End Handle child category selection change ******************* */

  /* **************** Start Fetch Product Based On Category, SubCategory and Child Category (Load In Select Box) ******************* */
  const productQueryParams = useMemo(() => {
    const params = {};

    if (selectedCategoryId && selectedCategoryId !== "") {
      params.category_id = parseInt(selectedCategoryId);
    }

    if (selectedSubCategoryId && selectedSubCategoryId !== "") {
      params.sub_category_id = parseInt(selectedSubCategoryId);
    }

    if (selectedChildCategoryId && selectedChildCategoryId !== "") {
      params.child_category_id = parseInt(selectedChildCategoryId);
    }

    return params;
  }, [selectedCategoryId, selectedSubCategoryId, selectedChildCategoryId]);

  const { data: productListResp } = useListAllProductQuery(
    productQueryParams,
     {skip: !selectedCategoryId || selectedCategoryId === "" }
  );

  const productList = productListResp?.data || [];
  const productAry = productList.map((product) => ({
    value: product.id,
    label: product.title,
  }));
  /* **************** End Fetch Product Based On Category, SubCategory and Child Category (Load In Select Box) ******************* */

  /* **************** Start Fetch Product Variations and Attributes ******************* */
  const [getVariationsProducts] = useGetVariationsProductsMutation();
  const [productAttributes, setProductAttributes] = useState([]);
  // Fetch product variations when product is selected
  useEffect(() => {
    if (selectedProductId && selectedProductId !== "") {
      const fetchProductVariations = async () => {
        try {
          const response = await getVariationsProducts({ product_id: parseInt(selectedProductId) }).unwrap();
          if (response?.data && Array.isArray(response.data) && response.data.length > 0) {
            setProductAttributes(response.data);
          } else {
            setProductAttributes([]);
          }
        } catch (error) {
          console.error("Error fetching product variations:", error);
          setProductAttributes([]);
        }
      };
      fetchProductVariations();
    } else {
      setProductAttributes([]);
    }
  }, [selectedProductId, getVariationsProducts]);

  const { data: allAttributesData } = useListAllAttributesQuery();
  const attributesMap = useMemo(() => {
    if (!allAttributesData?.data) return {};
    return allAttributesData.data.reduce((acc, attr) => {
      acc[attr.id] = attr.title;
      return acc;
    }, {});
  }, [allAttributesData?.data]);
  /* **************** End Fetch Product Variations and Attributes ******************* */

  /* **************** Start Edit Inventory ******************* */
  const [handleCreateInventoryApi] = useCreateInventoryMutation();
  const handleSubmit = async (body) => {
    try {

      const attributes = [];
      Object.keys(body).forEach(key => {
        if (key.startsWith('attribute_') && body[key] && body[key] !== "") {
          attributes.push(parseInt(body[key]));
        }
      });

      const inventoryData = {
        branch_id: parseInt(body.branch_id),
        product_id: parseInt(body.product_id),
        attributes: attributes.length > 0 ? JSON.stringify(attributes) : null,
        stock_quantity: parseInt(body.stock_quantity),
        stock_status: parseInt(body.stock_status),
        sku: body.sku,
        bar_code: body.bar_code,
        original_price: body.original_price,
        selling_price: body.selling_price,
        old_price: body.old_price || "",
        description: body.description || "",
        description_ar: body.description_ar || ""
      };

      const resp = await handleCreateInventoryApi(inventoryData).unwrap();
      handleApiSuccess(resp);
      navigate("/inventory");
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Edit Inventory ******************* */

  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid ">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card">
              <div className="card-body">
                <div className="tab-content" id="pills-tabContent">
                  <div
                    className="tab-pane fade show active"
                    id="pills-account"
                    role="tabpanel"
                    aria-labelledby="pills-account-tab"
                    tabIndex="0"
                    >
                    <div className="row">
                      <div className="col-lg-12 d-flex align-items-stretch">
                        <div className="card w-100 border position-relative overflow-hidden mb-0">
                          <div className="card-body p-4">
                            <h4 className="card-title">Add Inventory</h4>
                            <p className="card-subtitle mb-4">
                              To add Inventory, fill details and save from here
                            </p>
                            <Formik
                              initialValues={getInitialValues(productAttributes, currentFormValuesRef.current)}
                              enableReinitialize={true}
                              validationSchema={getValidationSchema(productAttributes, attributesMap)}
                              onSubmit={handleSubmit}
                            >
                              {(formikProps) => {
                                currentFormValuesRef.current = formikProps.values;
                                const handleProductSelection = (e) => {
                                  const productId = e.target.value;
                                  setSelectedProductId(productId);
                                  currentFormValuesRef.current = { ...formikProps.values, product_id: productId }
                                };
                                return (
                              <Form
                                name="product-create"
                                className="needs-validation"
                                autoComplete="off"
                              >
                                <div className="row">
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label htmlFor="category_id" className="form-label">
                                        Category
                                        <span className="un-validation">(*)</span>
                                      </label>
                                      <FormikField
                                        name="category_id"
                                        id="category_id"
                                        className="form-select"
                                        type="select"
                                        options={categoriesList}
                                        onChange={handleCategoryChange}
                                      />
                                    </div>
                                  </div>
                                  {selectedCategoryId && selectedCategoryId !== "" && subCategoriesList.length > 0 && (
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label htmlFor="sub_category_id" className="form-label">
                                          Sub Category
                                        </label>
                                        <FormikField
                                          name="sub_category_id"
                                          id="sub_category_id"
                                          className="form-select"
                                          type="select"
                                          options={subCategoriesList}
                                          onChange={handleSubCategoryChange}
                                        />
                                      </div>
                                    </div>
                                  )}
                                  {selectedSubCategoryId && selectedSubCategoryId !== "" && childCategoriesList.length > 0 && (
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label htmlFor="child_category_id" className="form-label">
                                          Child Category
                                        </label>
                                        <FormikField
                                          name="child_category_id"
                                          id="child_category_id"
                                          className="form-select"
                                          type="select"
                                          options={childCategoriesList}
                                          onChange={handleChildCategoryChange}
                                        />
                                      </div>
                                    </div>
                                  )}
                                  {selectedCategoryId && selectedCategoryId !== "" && subCategoriesList.length > 0 && (
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="product_id"
                                        className="form-label"
                                      >
                                        Product
                                        <span className="un-validation">(*)</span>
                                      </label>
                                      <FormikField
                                        name="product_id"
                                        id="product_id"
                                        className="form-select"
                                        type="select"
                                        options={productAry}
                                        onChange={handleProductSelection}
                                      />
                                    </div>
                                  </div>
                                  )}                                                            
                                  {selectedProductId && selectedProductId !== "" && (
                                    <>
                                    <hr></hr>
                                    <h4 className="card-title">Inventory Details</h4>
                                    <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="branch_id" className="form-label">
                                            Branch
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            name="branch_id"
                                            id="branch_id"
                                            className="form-select"
                                            type="select"
                                            options={branchesList}
                                          />
                                        </div>
                                      </div>                                    
                                    {/* Start Dynamic Attribute Value Select Fields */}
                                    {productAttributes.length > 0 && (
                                      <>
                                        {productAttributes.map((attr) => (
                                          <AttributeValueSelect
                                            key={attr.attribute_id}
                                            attributeId={attr.attribute_id}
                                            attributeTitle={attributesMap[attr.attribute_id] || `Attribute ${attr.attribute_id}`}
                                          />
                                        ))}
                                      </>
                                    )}
                                    {/* End Dynamic Attribute Value Select Fields */}                                      
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="bar_code" className="form-label">
                                            Barcode
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            type="text"
                                            name="bar_code"
                                            id="bar_code"
                                            placeholder="Enter barcode"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="sku" className="form-label">
                                            Sku
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            type="text"
                                            name="sku"
                                            id="sku"
                                            placeholder="Enter sku"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="original_price" className="form-label">
                                            Original Price
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            type="number"
                                            name="original_price"
                                            id="original_price"
                                            placeholder="Enter orignal price"
                                            className="form-control"
                                            step="0.01"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="old_price" className="form-label">
                                            Old Price
                                          </label>
                                          <FormikField
                                            type="number"
                                            name="old_price"
                                            id="old_price"
                                            placeholder="Enter old price"
                                            className="form-control"
                                            step="0.01"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="selling_price" className="form-label">
                                            Selling Price
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            type="number"
                                            name="selling_price"
                                            id="selling_price"
                                            placeholder="Enter selling price"
                                            className="form-control"
                                            step="0.01"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="stock_quantity" className="form-label">
                                            Stock Quantity
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            type="number"
                                            name="stock_quantity"
                                            id="stock_quantity"
                                            placeholder="Enter stock quantity"
                                            className="form-control"
                                            min="0"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="stock_status" className="form-label">
                                            Stock Status
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            type="select"
                                            name="stock_status"
                                            id="stock_status"
                                            className="form-select"
                                            options={stockStatusList}
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="description" className="form-label">
                                            Description
                                          </label>
                                          <FormikField
                                            type="textarea"
                                            name="description"
                                            id="description"
                                            placeholder="Enter description"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="description_ar" className="form-label">
                                            Description Arabic
                                          </label>
                                          <FormikField
                                            type="textarea"
                                            name="description_ar"
                                            id="description_ar"
                                            placeholder="Enter description arabic"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                    </>
                                  )}
                                  <div className="col-12">
                                    <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                      <button
                                        className="btn btn-primary"
                                        type="submit"
                                      >
                                        Add Inventory
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </Form>
                                );
                              }}
                            </Formik>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CommonFooter />
    </>
  );
}
