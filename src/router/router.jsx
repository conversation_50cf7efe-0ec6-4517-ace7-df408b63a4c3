import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Router<PERSON>rovider } from "react-router-dom";

import { LoginPage } from "../pages/login";
import { DashboardPage } from "../pages/dashboard";
import ProtectedRoute from "./protectedRoute";
import AuthRoute from "./authRoutes";
import { Account } from "../pages/account_settings";
import { ShopProfle } from "../pages/shop_profile";
import { BranchList } from "../pages/branch";
import { CreateBranch } from "../pages/branch/create";
import { EditBranch } from "../pages/branch/update";
import { RoleList } from "../pages/roles";
import { StaffList } from "../pages/staffs";
import { CreateStaff } from "../pages/staffs/create";
import EditStaff from "../pages/staffs/update/edit_staff";
import { Categories } from "../pages/categories";
import { SubCategories } from "../pages/subcategories";
import { ChildCategories } from "../pages/childcategories";
import { Attributes } from "../pages/attributes";
import { AttributesValues } from "../pages/attributes_values";
import { Brands } from "../pages/brands";
import { ProductList } from "../pages/products";
import { CreateProduct } from "../pages/products/create";
import { EditProduct } from "../pages/products/update";
import { Inventory } from "../pages/inventory";
import { CreateInventory } from "../pages/inventory/create";
import { EditInventory } from "../pages/inventory/update";
import InventoryHistory from "../pages/inventory/history/inventory_history";
const router = createBrowserRouter([
  {
    path: "/",
    //element: <DashboardLayout />,
    children: [
      {
        index: true,
        element: (
          <AuthRoute>
            <LoginPage />
          </AuthRoute>
        ),
      },
      {
        path: "/login",
        element: (
          <AuthRoute>
            <LoginPage />
          </AuthRoute>
        ),
      },
      {
        path: "/dashboard",
        element: (
          <ProtectedRoute>
            <DashboardPage />
          </ProtectedRoute>
        ),
      },
      {
        path: "/account",
        element: (
          <ProtectedRoute>
            <Account />
          </ProtectedRoute>
        ),
      },
      {
        path: "/shop_profile",
        element: (
          <ProtectedRoute>
            <ShopProfle />
          </ProtectedRoute>
        ),
      },
      {
        path: "/branch_list",
        element: (
          <ProtectedRoute>
            <BranchList />
          </ProtectedRoute>
        ),
      },
      {
        path: "/createBranch",
        element: (
          <ProtectedRoute>
            <CreateBranch />
          </ProtectedRoute>
        ),
      },
      {
        path: "/editBranch",
        element: (
          <ProtectedRoute>
            <EditBranch />
          </ProtectedRoute>
        ),
      },
      {
        path: "/roles",
        element: (
          <ProtectedRoute>
            <RoleList />
          </ProtectedRoute>
        ),
      },
      {
        path: "/staffs",
        element: (
          <ProtectedRoute>
            <StaffList />
          </ProtectedRoute>
        ),
      },
      {
        path: "/createStaff",
        element: (
          <ProtectedRoute>
            <CreateStaff />
          </ProtectedRoute>
        ),
      },
      {
        path: "/editStaff",
        element: (
          <ProtectedRoute>
            <EditStaff />
          </ProtectedRoute>
        ),
      },
      {
        path: "/mainCategories",
        element: (
          <ProtectedRoute>
            <Categories />
          </ProtectedRoute>
        ),
      },
      {
        path: "/subCategories",
        element: (
          <ProtectedRoute>
            <SubCategories />
          </ProtectedRoute>
        ),
      },
      {
        path: "/childCategories",
        element: (
          <ProtectedRoute>
            <ChildCategories />
          </ProtectedRoute>
        ),
      },
      {
        path: "/attributes",
        element: (
          <ProtectedRoute>
            <Attributes />
          </ProtectedRoute>
        ),
      },
      {
        path: "/attributesValues",
        element: (
          <ProtectedRoute>
            <AttributesValues />
          </ProtectedRoute>
        ),
      },
      {
        path: "/brands",
        element: (
          <ProtectedRoute>
            <Brands />
          </ProtectedRoute>
        ),
      },
      {
        path: "/products",
        element: (
          <ProtectedRoute>
            <ProductList />
          </ProtectedRoute>
        ),
      },
      {
        path: "/createProduct",
        element: (
          <ProtectedRoute>
            <CreateProduct />
          </ProtectedRoute>
        ),
      },
      {
        path: "/editProduct",
        element: (
          <ProtectedRoute>
            <EditProduct />
          </ProtectedRoute>
        ),
      },
      {
        path: "/inventory",
        element: (
          <ProtectedRoute>
            <Inventory />
          </ProtectedRoute>
        ),
      },
      {
        path: "/createInventory",
        element: (
          <ProtectedRoute>
            <CreateInventory />
          </ProtectedRoute>
        ),
      },
      {
        path: "/editInventory",
        element: (
          <ProtectedRoute>
            <EditInventory />
          </ProtectedRoute>
        ),
      },
      {
        path: "/inventoryHistory",
        element: (
          <ProtectedRoute>
            <InventoryHistory />
          </ProtectedRoute>
        ),
      },
    ],
  },
  {
    path: "*",
    element: <div> Not found! </div>,
  },
]);

export default function Router() {
  return <RouterProvider router={router} />;
}
